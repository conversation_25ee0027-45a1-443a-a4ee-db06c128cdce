---
// layout
import BaseLayout from "@layouts/BaseLayout.astro";

// components
import PostCard from "@components/PostCard/PostCardAtlas.astro";
import CategoryCloud from "@components/CategoryCloud/CategoryCloud.astro";
import Badge from "@components/Badge/Badge.astro";

// utils
import { getAllPosts } from "@js/blogUtils";

const posts = await getAllPosts("it");
---

<BaseLayout
	title="Il Mio Blog | Dott. Emanuele Belloni"
	description="Approfondimenti scientifici e consigli pratici su nutrizione, movimento e benessere over 40. La scienza della salute spiegata in modo semplice e chiaro."
>
	<section class="site-container">
		<div
			class="overflow-x-clip bg-[url('/assets/pattern-light.svg')] bg-center bg-no-repeat pt-24 md:pt-32 dark:bg-[url('/assets/pattern-dark.svg')]"
		>
			<div class="mx-auto flex max-w-[950px] flex-col justify-center">
				<div class="mx-auto">
					<Badge>📂 Blog</Badge>
				</div>
				<h1 class="h1 text-center">
					La scienza accessibile a tutti (⚠️ in costruzione)
				</h1>
			</div>

			<div class="mt-20 flex pb-8 md:mt-28">
				<CategoryCloud />
			</div>
		</div>

		<div class="grid gap-8 gap-y-10 md:grid-cols-2">
			{posts.map((post) => <PostCard post={post} showDescription={true} data-aos="fade-up" />)}
		</div>
	</section>
</BaseLayout>